import { useState, useEffect } from 'react'
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd'
import { motion, AnimatePresence } from 'framer-motion'
import { useAuth } from '../contexts/AuthContext'
import { db } from '../config/firebase'
import { collection, addDoc, updateDoc, deleteDoc, doc, onSnapshot, query, where } from 'firebase/firestore'
import { Plus, Calendar, User, X, Edit3 } from 'lucide-react'
import { PaperClipIcon } from '@heroicons/react/24/outline'
import Layout from '../components/Layout'
import CardModal from '../components/CardModal'
import { SkeletonBoard } from '../components/LoadingSpinner'
import toast from 'react-hot-toast'

export default function Dashboard() {
  const [boards, setBoards] = useState([])
  const [newBoardName, setNewBoardName] = useState('')
  const [showNewBoard, setShowNewBoard] = useState(false)
  const [selectedCard, setSelectedCard] = useState(null)
  const [isCardModalOpen, setIsCardModalOpen] = useState(false)
  const [loading, setLoading] = useState(true)
  const [currentBoardId, setCurrentBoardId] = useState(null)
  const [currentListId, setCurrentListId] = useState(null)
  const { currentUser, logout } = useAuth()

  useEffect(() => {
    if (currentUser) {
      const q = query(collection(db, 'boards'), where('userId', '==', currentUser.uid))
      const unsubscribe = onSnapshot(q, (snapshot) => {
        const boardsData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }))
        setBoards(boardsData)
        setLoading(false)
      })
      return unsubscribe
    }
  }, [currentUser])

  const createBoard = async () => {
    if (!newBoardName.trim()) return

    const loadingToast = toast.loading('Creating board...')

    try {
      await addDoc(collection(db, 'boards'), {
        name: newBoardName.trim(),
        userId: currentUser.uid,
        lists: [
          { id: 'todo', title: 'To Do', cards: [] },
          { id: 'inprogress', title: 'In Progress', cards: [] },
          { id: 'done', title: 'Done', cards: [] }
        ],
        createdAt: new Date()
      })

      setNewBoardName('')
      setShowNewBoard(false)
      toast.success('Board created successfully!', { id: loadingToast })
    } catch (error) {
      console.error('Error creating board:', error)
      toast.error('Failed to create board', { id: loadingToast })
    }
  }

  const openCardModal = (card = null, boardId = null, listId = null) => {
    setSelectedCard(card)
    setCurrentBoardId(boardId)
    setCurrentListId(listId)
    setIsCardModalOpen(true)
  }

  const closeCardModal = () => {
    setSelectedCard(null)
    setCurrentBoardId(null)
    setCurrentListId(null)
    setIsCardModalOpen(false)
  }

  const saveCard = async (cardData) => {
    const loadingToast = toast.loading(selectedCard ? 'Updating card...' : 'Creating card...')

    try {
      const board = boards.find(b => b.id === currentBoardId)
      if (!board) return

      let updatedLists

      if (selectedCard) {
        // Update existing card
        updatedLists = board.lists.map(list => ({
          ...list,
          cards: (list.cards || []).map(card =>
            card.id === selectedCard.id ? cardData : card
          )
        }))
      } else {
        // Create new card
        const newCard = {
          ...cardData,
          id: `card-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
          createdAt: new Date().toISOString()
        }

        updatedLists = board.lists.map(list =>
          list.id === currentListId
            ? { ...list, cards: [...(list.cards || []), newCard] }
            : list
        )
      }

      await updateDoc(doc(db, 'boards', currentBoardId), { lists: updatedLists })
      toast.success(selectedCard ? 'Card updated successfully!' : 'Card created successfully!', { id: loadingToast })
    } catch (error) {
      console.error('Error saving card:', error)
      toast.error('Failed to save card', { id: loadingToast })
    }
  }

  const addCard = async (boardId, listId, title) => {
    openCardModal(null, boardId, listId)
  }

  const deleteCard = async (cardId) => {
    const loadingToast = toast.loading('Deleting card...')

    try {
      const board = boards.find(b => b.id === currentBoardId)
      if (!board) return

      const updatedLists = board.lists.map(list => ({
        ...list,
        cards: (list.cards || []).filter(c => c.id !== cardId)
      }))

      await updateDoc(doc(db, 'boards', currentBoardId), { lists: updatedLists })
      toast.success('Card deleted successfully!', { id: loadingToast })
    } catch (error) {
      console.error('Error deleting card:', error)
      toast.error('Failed to delete card', { id: loadingToast })
    }
  }

  const onDragEnd = async (result) => {
    if (!result.destination) return

    const { source, destination, draggableId } = result
    
    if (source.droppableId === destination.droppableId && source.index === destination.index) {
      return
    }

    const sourceBoardId = source.droppableId.split('-')[0]
    const destBoardId = destination.droppableId.split('-')[0]
    
    if (sourceBoardId !== destBoardId) return
    
    const board = boards.find(b => b.id === sourceBoardId)
    if (!board || !board.lists) return
    
    const sourceListId = source.droppableId.split('-')[1]
    const destListId = destination.droppableId.split('-')[1]
    
    const sourceList = board.lists.find(l => l.id === sourceListId)
    if (!sourceList || !sourceList.cards) return 
    
    const card = sourceList.cards.find(c => c.id === draggableId)
    if (!card) return

    try {
      let updatedLists
      
      if (sourceListId === destListId) {
        // Same list reordering
        const newCards = [...sourceList.cards]
        newCards.splice(source.index, 1)
        newCards.splice(destination.index, 0, card)
        
        updatedLists = board.lists.map(list => 
          list.id === sourceListId 
            ? { ...list, cards: newCards }
            : list
        )
      } else {
        // Moving between different lists
        updatedLists = board.lists.map(list => {
          if (list.id === sourceListId) {
            return { ...list, cards: list.cards.filter(c => c.id !== draggableId) }
          }
          if (list.id === destListId) {
            const newCards = [...(list.cards || [])]
            newCards.splice(destination.index, 0, card)
            return { ...list, cards: newCards }
          }
          return list
        })
      }
      
      await updateDoc(doc(db, 'boards', sourceBoardId), { lists: updatedLists })
    } catch (error) {
      console.error('Error updating card position:', error)
    }
  }

  if (loading) {
    return (
      <Layout>
        <div className="p-8">
          <SkeletonBoard />
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="p-8"
      >
        <div className="mb-8">
          <div className="flex justify-between items-center mb-6">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
            >
              <h1 className="text-2xl font-bold text-gray-900">Kanban Boards</h1>
              <p className="text-gray-600">Manage your projects and tasks</p>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
              className="flex space-x-4"
            >
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setShowNewBoard(true)}
                className="flex items-center space-x-2 bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors"
              >
                <Plus size={20} />
                <span>New Board</span>
              </motion.button>
            </motion.div>
          </div>

          <AnimatePresence>
            {showNewBoard && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
                className="mb-4 p-4 bg-white rounded-lg shadow overflow-hidden"
              >
                <input
                  type="text"
                  placeholder="Board name"
                  value={newBoardName}
                  onChange={(e) => setNewBoardName(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && createBoard()}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-all"
                  autoFocus
                />
                <div className="mt-2 space-x-2">
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={createBoard}
                    className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors"
                  >
                    Create
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => setShowNewBoard(false)}
                    className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 transition-colors"
                  >
                    Cancel
                  </motion.button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {boards.length === 0 ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-12"
          >
            <p className="text-gray-500">No boards yet. Create your first board to get started!</p>
          </motion.div>
        ) : (
          <AnimatePresence>
            {boards.map((board, index) => (
              <motion.div
                key={board.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ delay: index * 0.1 }}
                className="mb-8 bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow"
              >
                <h3 className="text-lg font-semibold mb-4">{board.name}</h3>
            
            <DragDropContext onDragEnd={onDragEnd}>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {board.lists?.map(list => (
                  <div key={list.id} className="bg-gray-100 rounded-lg p-4">
                    <div className="flex justify-between items-center mb-4">
                      <h4 className="font-medium text-gray-900">{list.title}</h4>
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        onClick={() => addCard(board.id, list.id)}
                        className="text-gray-500 hover:text-gray-700 p-1 rounded-md hover:bg-gray-200 transition-colors"
                      >
                        <Plus size={16} />
                      </motion.button>
                    </div>
                    
                    <Droppable droppableId={`${board.id}-${list.id}`}>
                      {(provided, snapshot) => (
                        <div
                          {...provided.droppableProps}
                          ref={provided.innerRef}
                          className={`space-y-2 min-h-[200px] p-2 rounded-md transition-colors ${
                            snapshot.isDraggingOver ? 'bg-blue-50 border-2 border-blue-200' : ''
                          }`}
                        >
                          {(list.cards || []).map((card, index) => (
                            <Draggable key={card.id} draggableId={card.id} index={index}>
                              {(provided) => (
                                <motion.div
                                  ref={provided.innerRef}
                                  {...provided.draggableProps}
                                  initial={{ opacity: 0, scale: 0.8 }}
                                  animate={{ opacity: 1, scale: 1 }}
                                  whileHover={{ scale: 1.02 }}
                                  className="bg-white p-3 rounded-md shadow-sm border hover:shadow-md transition-all relative group cursor-pointer"
                                  onClick={() => openCardModal(card, board.id, list.id)}
                                >
                                  <div {...provided.dragHandleProps} className="cursor-grab active:cursor-grabbing">
                                    <div className="flex justify-between items-start mb-2">
                                      <h5 className="font-medium text-gray-900 flex-1">{card.title}</h5>
                                      <motion.button
                                        whileHover={{ scale: 1.2 }}
                                        whileTap={{ scale: 0.8 }}
                                        onClick={(e) => {
                                          e.stopPropagation()
                                          openCardModal(card, board.id, list.id)
                                        }}
                                        className="opacity-0 group-hover:opacity-100 text-gray-400 hover:text-gray-600 ml-2 p-1 rounded-md hover:bg-gray-100 transition-all"
                                      >
                                        <Edit3 size={14} />
                                      </motion.button>
                                    </div>
                                    {card.description && (
                                      <p className="text-sm text-gray-600 mb-2 line-clamp-2">{card.description}</p>
                                    )}

                                    {/* Priority Badge */}
                                    {card.priority && (
                                      <div className="mb-2">
                                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                          card.priority === 'high' ? 'bg-red-100 text-red-800' :
                                          card.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                          'bg-green-100 text-green-800'
                                        }`}>
                                          {card.priority}
                                        </span>
                                      </div>
                                    )}

                                    <div className="flex items-center justify-between text-xs text-gray-500">
                                      <div className="flex items-center space-x-2">
                                        {card.assignee && (
                                          <div className="flex items-center space-x-1">
                                            <User size={12} />
                                            <span>{card.assignee}</span>
                                          </div>
                                        )}
                                        {card.attachments && card.attachments.length > 0 && (
                                          <div className="flex items-center space-x-1">
                                            <PaperClipIcon className="h-3 w-3" />
                                            <span>{card.attachments.length}</span>
                                          </div>
                                        )}
                                      </div>
                                      {card.dueDate && (
                                        <div className="flex items-center space-x-1">
                                          <Calendar size={12} />
                                          <span>{card.dueDate}</span>
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </motion.div>
                              )}
                            </Draggable>
                          ))}
                          {provided.placeholder}
                        </div>
                      )}
                    </Droppable>
                  </div>
                ))}
              </div>
            </DragDropContext>
              </motion.div>
            ))}
          </AnimatePresence>
        )}

        {/* Card Modal */}
        <CardModal
          isOpen={isCardModalOpen}
          onClose={closeCardModal}
          card={selectedCard}
          onSave={saveCard}
          onDelete={deleteCard}
          boardMembers={[]} // You can add board members here later
        />
      </motion.div>
    </Layout>
  )
}