import { useState, useEffect } from 'react'
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd'
import { useAuth } from '../contexts/AuthContext'
import { db } from '../config/firebase'
import { collection, addDoc, updateDoc, deleteDoc, doc, onSnapshot, query, where } from 'firebase/firestore'
import { Plus, Calendar, User, X } from 'lucide-react'
import Layout from '../components/Layout'

export default function Dashboard() {
  const [boards, setBoards] = useState([])
  const [newBoardName, setNewBoardName] = useState('')
  const [showNewBoard, setShowNewBoard] = useState(false)
  const { currentUser, logout } = useAuth()

  useEffect(() => {
    if (currentUser) {
      const q = query(collection(db, 'boards'), where('userId', '==', currentUser.uid))
      const unsubscribe = onSnapshot(q, (snapshot) => {
        const boardsData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }))
        setBoards(boardsData)
      })
      return unsubscribe
    }
  }, [currentUser])

  const createBoard = async () => {
    if (!newBoardName.trim()) return
    
    try {
      await addDoc(collection(db, 'boards'), {
        name: newBoardName.trim(),
        userId: currentUser.uid,
        lists: [
          { id: 'todo', title: 'To Do', cards: [] },
          { id: 'inprogress', title: 'In Progress', cards: [] },
          { id: 'done', title: 'Done', cards: [] }
        ],
        createdAt: new Date()
      })
      
      setNewBoardName('')
      setShowNewBoard(false)
    } catch (error) {
      console.error('Error creating board:', error)
    }
  }

  const addCard = async (boardId, listId, title) => {
    try {
      const board = boards.find(b => b.id === boardId)
      if (!board) return
      
      const newCard = {
        id: `card-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        title,
        description: '',
        assignee: '',
        dueDate: '',
        createdAt: new Date()
      }
      
      const updatedLists = board.lists.map(list => 
        list.id === listId 
          ? { ...list, cards: [...(list.cards || []), newCard] }
          : list
      )
      
      await updateDoc(doc(db, 'boards', boardId), { lists: updatedLists })
    } catch (error) {
      console.error('Error adding card:', error)
    }
  }

  const deleteCard = async (boardId, listId, cardId) => {
    try {
      const board = boards.find(b => b.id === boardId)
      if (!board) return
      
      const updatedLists = board.lists.map(list => 
        list.id === listId 
          ? { ...list, cards: (list.cards || []).filter(c => c.id !== cardId) }
          : list
      )
      
      await updateDoc(doc(db, 'boards', boardId), { lists: updatedLists })
    } catch (error) {
      console.error('Error deleting card:', error)
    }
  }

  const onDragEnd = async (result) => {
    if (!result.destination) return

    const { source, destination, draggableId } = result
    
    if (source.droppableId === destination.droppableId && source.index === destination.index) {
      return
    }

    const sourceBoardId = source.droppableId.split('-')[0]
    const destBoardId = destination.droppableId.split('-')[0]
    
    if (sourceBoardId !== destBoardId) return
    
    const board = boards.find(b => b.id === sourceBoardId)
    if (!board || !board.lists) return
    
    const sourceListId = source.droppableId.split('-')[1]
    const destListId = destination.droppableId.split('-')[1]
    
    const sourceList = board.lists.find(l => l.id === sourceListId)
    if (!sourceList || !sourceList.cards) return 
    
    const card = sourceList.cards.find(c => c.id === draggableId)
    if (!card) return

    try {
      let updatedLists
      
      if (sourceListId === destListId) {
        // Same list reordering
        const newCards = [...sourceList.cards]
        newCards.splice(source.index, 1)
        newCards.splice(destination.index, 0, card)
        
        updatedLists = board.lists.map(list => 
          list.id === sourceListId 
            ? { ...list, cards: newCards }
            : list
        )
      } else {
        // Moving between different lists
        updatedLists = board.lists.map(list => {
          if (list.id === sourceListId) {
            return { ...list, cards: list.cards.filter(c => c.id !== draggableId) }
          }
          if (list.id === destListId) {
            const newCards = [...(list.cards || [])]
            newCards.splice(destination.index, 0, card)
            return { ...list, cards: newCards }
          }
          return list
        })
      }
      
      await updateDoc(doc(db, 'boards', sourceBoardId), { lists: updatedLists })
    } catch (error) {
      console.error('Error updating card position:', error)
    }
  }

  return (
    <Layout>
      <div className="p-8">
        <div className="mb-8">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Kanban Boards</h1>
              <p className="text-gray-600">Manage your projects and tasks</p>
            </div>
            <div className="flex space-x-4">
              <button
                onClick={() => setShowNewBoard(true)}
                className="flex items-center space-x-2 bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700"
              >
                <Plus size={20} />
                <span>New Board</span>
              </button>
            </div>
            
          </div>

          {showNewBoard && (
            <div className="mb-4 p-4 bg-white rounded-lg shadow">
              <input
                type="text"
                placeholder="Board name"
                value={newBoardName}
                onChange={(e) => setNewBoardName(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && createBoard()}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              />
              <div className="mt-2 space-x-2">
                <button
                  onClick={createBoard}
                  className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700"
                >
                  Create
                </button>
                <button
                  onClick={() => setShowNewBoard(false)}
                  className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400"
                >
                  Cancel
                </button>
              </div>
            </div>
          )}
        </div>

        {boards.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500">No boards yet. Create your first board to get started!</p>
          </div>
        ) : (
          boards.map(board => (
            <div key={board.id} className="mb-8 bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-4">{board.name}</h3>
            
            <DragDropContext onDragEnd={onDragEnd}>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {board.lists?.map(list => (
                  <div key={list.id} className="bg-gray-100 rounded-lg p-4">
                    <div className="flex justify-between items-center mb-4">
                      <h4 className="font-medium text-gray-900">{list.title}</h4>
                      <button
                        onClick={() => {
                          const title = prompt('Card title:')
                          if (title && title.trim()) {
                            addCard(board.id, list.id, title.trim())
                          }
                        }}
                        className="text-gray-500 hover:text-gray-700"
                      >
                        <Plus size={16} />
                      </button>
                    </div>
                    
                    <Droppable droppableId={`${board.id}-${list.id}`}>
                      {(provided, snapshot) => (
                        <div
                          {...provided.droppableProps}
                          ref={provided.innerRef}
                          className={`space-y-2 min-h-[200px] p-2 rounded-md transition-colors ${
                            snapshot.isDraggingOver ? 'bg-blue-50 border-2 border-blue-200' : ''
                          }`}
                        >
                          {(list.cards || []).map((card, index) => (
                            <Draggable key={card.id} draggableId={card.id} index={index}>
                              {(provided) => (
                                <div
                                  ref={provided.innerRef}
                                  {...provided.draggableProps}
                                  className="bg-white p-3 rounded-md shadow-sm border hover:shadow-md transition-shadow relative group"
                                >
                                  <div {...provided.dragHandleProps} className="cursor-grab active:cursor-grabbing">
                                    <div className="flex justify-between items-start mb-2">
                                      <h5 className="font-medium text-gray-900 flex-1">{card.title}</h5>
                                      <button
                                        onClick={(e) => {
                                          e.stopPropagation()
                                          if (confirm('Delete this card?')) {
                                            deleteCard(board.id, list.id, card.id)
                                          }
                                        }}
                                        className="opacity-0 group-hover:opacity-100 text-red-500 hover:text-red-700 ml-2"
                                      >
                                        <X size={14} />
                                      </button>
                                    </div>
                                    {card.description && (
                                      <p className="text-sm text-gray-600 mb-2">{card.description}</p>
                                    )}
                                    <div className="flex items-center justify-between text-xs text-gray-500">
                                      {card.assignee && (
                                        <div className="flex items-center space-x-1">
                                          <User size={12} />
                                          <span>{card.assignee}</span>
                                        </div>
                                      )}
                                      {card.dueDate && (
                                        <div className="flex items-center space-x-1">
                                          <Calendar size={12} />
                                          <span>{card.dueDate}</span>
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              )}
                            </Draggable>
                          ))}
                          {provided.placeholder}
                        </div>
                      )}
                    </Droppable>
                  </div>
                ))}
              </div>
            </DragDropContext>
            </div>
          ))
        )}
      </div>
    </Layout>
  )
}