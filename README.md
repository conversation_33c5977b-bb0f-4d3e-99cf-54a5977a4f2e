# TaskFlow Pro - Advanced Kanban Project Management

A modern, subscription-based Kanban board application built with React, Firebase, and Razorpay integration. TaskFlow Pro offers a comprehensive project management solution with multiple subscription tiers and premium features.

## Features

### Core Functionality
- **Drag & Drop Kanban Boards** - Intuitive task management with smooth animations
- **Real-time Collaboration** - Live updates across all connected users
- **Multiple Board Support** - Organize different projects separately
- **Card Management** - Detailed task cards with descriptions, attachments, and metadata

### Subscription Plans

#### Free Plan
- 3 personal boards
- 5 team members
- Basic templates
- 10MB file storage
- Email support

#### Pro Plan (₹299/month)
- 50 boards
- 25 team members
- Premium templates (Sprint Planning, Marketing Campaign, Sales Pipeline)
- 100GB file storage
- Priority support
- Advanced analytics
- Time tracking
- Custom fields
- Priority levels
- Due dates and reminders
- 14-day free trial

#### Team Plan (₹599/month)
- 200 boards
- 100 team members
- Advanced templates (Product Roadmap, HR Recruitment)
- 500GB file storage
- Phone support
- Team analytics
- Advanced permissions
- Custom integrations
- Automation features
- Calendar view
- Bulk operations
- 14-day free trial

#### Enterprise Plan
- Unlimited boards and members
- Unlimited storage
- Dedicated support
- Custom development
- On-premise deployment
- Advanced security
- SLA guarantee
- API access

### Premium Features

#### Time Tracking (Pro+)
- Start/stop timers for tasks
- Productivity analytics
- Time efficiency metrics
- Detailed time reports

#### Custom Fields (Pro+)
- Add unlimited custom data fields
- Flexible data types
- Enhanced task metadata

#### Priority Management (Pro+)
- 4-tier priority system (Low, Medium, High, Urgent)
- Visual priority indicators
- Priority-based analytics

#### Team Analytics (Team+)
- Individual performance tracking
- Task distribution analysis
- Team productivity insights
- Top performer identification

#### Advanced Templates
- Industry-specific workflows
- Professional project templates
- Customizable board structures

## Technology Stack

- **Frontend**: React 18, Vite, Tailwind CSS
- **Backend**: Firebase (Firestore, Authentication, Storage)
- **Payment Processing**: Razorpay
- **Animations**: Framer Motion
- **Drag & Drop**: @hello-pangea/dnd
- **State Management**: React Context API
- **Routing**: React Router DOM
- **Notifications**: React Hot Toast

## Installation

### Prerequisites
- Node.js (version 16 or higher)
- npm or yarn
- Firebase project
- Razorpay account (for payments)

### Setup Instructions

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd taskflow-pro
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   Create a `.env.local` file in the root directory:
   ```env
   # Firebase Configuration
   VITE_FIREBASE_API_KEY=your_firebase_api_key
   VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
   VITE_FIREBASE_PROJECT_ID=your_project_id
   VITE_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
   VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
   VITE_FIREBASE_APP_ID=your_app_id
   VITE_FIREBASE_MEASUREMENT_ID=your_measurement_id

   # Razorpay Configuration
   VITE_RAZORPAY_KEY_ID=your_razorpay_key_id
   VITE_RAZORPAY_KEY_SECRET=your_razorpay_key_secret
   ```

4. **Firebase Setup**
   - Create a Firebase project
   - Enable Authentication (Email/Password)
   - Create Firestore database
   - Deploy security rules from `firestore.rules`

5. **Deploy Firestore Rules**
   ```bash
   firebase deploy --only firestore:rules
   ```

6. **Start Development Server**
   ```bash
   npm run dev
   ```

## Firebase Security Rules

Deploy the included `firestore.rules` file to ensure proper data security:

```bash
firebase deploy --only firestore:rules
```

The rules ensure users can only access their own data and subscription information.

## Payment Testing

### Test Mode Credentials (Razorpay)

**Successful Payment:**
- Card Number: 4111 1111 1111 1111
- Expiry: Any future date
- CVV: Any 3 digits

**Failed Payment (for testing):**
- Card Number: 4000 0000 0000 0002
- Expiry: Any future date
- CVV: Any 3 digits

**UPI Testing:**
- success@razorpay
- failure@razorpay

**Net Banking:**
- Use any test bank with credentials: razorpay/razorpay

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── Layout.jsx      # Main layout wrapper
│   ├── BoardCreationModal.jsx
│   ├── CardModal.jsx
│   ├── PremiumCardFeatures.jsx
│   └── UpgradeModal.jsx
├── contexts/           # React Context providers
│   ├── AuthContext.jsx
│   └── SubscriptionContext.jsx
├── pages/              # Main application pages
│   ├── LandingPage.jsx
│   ├── Dashboard.jsx
│   ├── Analytics.jsx
│   ├── SubscriptionPage.jsx
│   └── BillingPage.jsx
├── utils/              # Utility functions
│   └── debugSubscription.js
└── config/
    └── firebase.js     # Firebase configuration
```

## Key Features Implementation

### Subscription Management
- Automatic free plan assignment for new users
- Trial period management (14 days for Pro/Team)
- Payment processing with Razorpay
- Plan upgrade/downgrade functionality
- Usage tracking and limits enforcement

### Feature Gating
- Board creation limits based on subscription
- Premium template access control
- Advanced feature restrictions
- Visual upgrade prompts

### User Experience
- Smooth drag-and-drop interactions
- Real-time updates
- Responsive design
- Loading states and error handling
- Toast notifications

## Development

### Available Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

### Code Style
- ESLint configuration included
- Tailwind CSS for styling
- Component-based architecture
- Context API for state management

## Deployment

### Firebase Hosting
1. Build the project: `npm run build`
2. Install Firebase CLI: `npm install -g firebase-tools`
3. Login to Firebase: `firebase login`
4. Initialize hosting: `firebase init hosting`
5. Deploy: `firebase deploy`

### Environment Variables
Ensure all environment variables are properly configured in your hosting platform.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions:
- Email: <EMAIL>
- Documentation: Check the code comments and component documentation
- Issues: Use the GitHub issues tracker

## Changelog

### Version 1.0.0
- Initial release with full subscription system
- Drag & drop Kanban boards
- Premium features and templates
- Razorpay payment integration
- Multi-tier subscription plans
- Real-time collaboration
- Advanced analytics and reporting