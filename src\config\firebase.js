import { initializeApp } from 'firebase/app'
import { getAuth } from 'firebase/auth'
import { getFirestore } from 'firebase/firestore'

const firebaseConfig = {
  apiKey: "AIzaSyBxIFY5tbiVIZiGqS0YLMKfOKpcDLOymCY",
  authDomain: "kanban-e7515.firebaseapp.com",
  projectId: "kanban-e7515",
  storageBucket: "kanban-e7515.firebasestorage.app",
  messagingSenderId: "470513296238",
  appId: "1:470513296238:web:3968f66f93d3b6d8c8d77f",
  measurementId: "G-Q9SPL2RSBN"
};

const app = initializeApp(firebaseConfig)
export const auth = getAuth(app)
export const db = getFirestore(app)