# 🔧 Subscription Upgrade Troubleshooting Guide

## 🚨 Issue: "Failed to upgrade plan" for some users

### 🔍 **Root Cause Analysis**

The issue where `<EMAIL>` can upgrade but other users cannot is likely due to one of these factors:

1. **Firebase Security Rules** - Permissions not set correctly
2. **User Authentication State** - User not properly authenticated
3. **Subscription Document** - Missing or corrupted subscription document
4. **Firebase Project Permissions** - User not added to Firebase project

### 🛠️ **Step-by-Step Fix**

#### **1. Update Firebase Security Rules**

Go to Firebase Console → Firestore Database → Rules and update:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow authenticated users to read/write their own subscription
    match /subscriptions/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Allow authenticated users to read/write their own boards
    match /boards/{boardId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.createdBy;
    }
    
    // Temporary: Allow all authenticated users (for debugging)
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

#### **2. Debug User Authentication**

Add this to your browser console to check user state:

```javascript
// Check current user
console.log('Current user:', firebase.auth().currentUser)
console.log('User UID:', firebase.auth().currentUser?.uid)
console.log('User email:', firebase.auth().currentUser?.email)

// Check if user is properly authenticated
firebase.auth().onAuthStateChanged((user) => {
  if (user) {
    console.log('User is signed in:', user.uid)
  } else {
    console.log('User is signed out')
  }
})
```

#### **3. Check Subscription Document**

In Firebase Console → Firestore → Collections → `subscriptions`:

- Check if document exists for the failing user's UID
- If missing, the app should create it automatically
- If corrupted, delete and let app recreate

#### **4. Test with Debug Function**

On the subscription page, click the "Debug Subscription (Dev Only)" button to:
- Check user authentication
- Verify document permissions
- Test write operations
- Create missing documents

### 🧪 **Testing Steps**

#### **For Working User (<EMAIL>):**
1. Login → Check console for user UID
2. Go to subscription page → Click debug button
3. Check what permissions/documents exist

#### **For Failing User:**
1. Login → Check console for user UID
2. Go to subscription page → Click debug button
3. Compare output with working user
4. Check for permission errors

### 🔧 **Common Fixes**

#### **Fix 1: Recreate Subscription Document**
```javascript
// In browser console
const user = firebase.auth().currentUser;
const db = firebase.firestore();

// Delete existing document (if corrupted)
await db.collection('subscriptions').doc(user.uid).delete();

// Let the app recreate it by refreshing the page
window.location.reload();
```

#### **Fix 2: Force Document Creation**
```javascript
// In browser console
const user = firebase.auth().currentUser;
const db = firebase.firestore();

const defaultSubscription = {
  plan: 'free',
  status: 'active',
  startDate: new Date(),
  endDate: null,
  trialEndDate: null,
  isTrialActive: false,
  paymentMethod: null,
  lastPayment: null,
  nextBilling: null,
  usage: { boards: 0, members: 0, storage: 0 },
  createdAt: new Date(),
  createdBy: user.uid
};

await db.collection('subscriptions').doc(user.uid).set(defaultSubscription);
console.log('Subscription document created');
```

#### **Fix 3: Check Firebase Authentication**
```javascript
// Ensure user is properly authenticated
firebase.auth().currentUser.getIdToken(true).then((token) => {
  console.log('User token valid:', !!token);
}).catch((error) => {
  console.error('Token error:', error);
});
```

### 📊 **Debugging Checklist**

#### ✅ **User Authentication:**
- [ ] User is logged in (`firebase.auth().currentUser` exists)
- [ ] User UID is valid (not null/undefined)
- [ ] User email is verified (if required)
- [ ] User token is valid

#### ✅ **Firebase Permissions:**
- [ ] Firestore security rules allow user access
- [ ] User has read/write permissions to `subscriptions/{userId}`
- [ ] No permission-denied errors in console

#### ✅ **Subscription Document:**
- [ ] Document exists in Firestore
- [ ] Document structure is correct
- [ ] No corrupted fields
- [ ] User UID matches document ID

#### ✅ **Network & Environment:**
- [ ] No network errors in console
- [ ] Firebase config is correct
- [ ] Environment variables are set
- [ ] No CORS issues

### 🚀 **Quick Fix Commands**

#### **Reset User Subscription:**
```javascript
// Run in browser console
window.debugSubscription().then(() => {
  console.log('Debug complete - check console output');
});
```

#### **Force Subscription Creation:**
```javascript
// Run in browser console
const { auth, db } = window.firebase;
const user = auth.currentUser;

if (user) {
  const subscriptionRef = db.collection('subscriptions').doc(user.uid);
  subscriptionRef.set({
    plan: 'free',
    status: 'active',
    startDate: new Date(),
    usage: { boards: 0, members: 0, storage: 0 },
    createdBy: user.uid
  }).then(() => {
    console.log('Subscription reset successfully');
    window.location.reload();
  });
}
```

### 📞 **If Still Not Working**

1. **Check Browser Console** - Look for specific error messages
2. **Check Firebase Console** - Verify user exists in Authentication
3. **Check Network Tab** - Look for failed API calls
4. **Try Different Browser** - Rule out browser-specific issues
5. **Clear Cache** - Clear browser cache and localStorage

### 🎯 **Expected Behavior**

After fixes, all users should be able to:
- ✅ Login successfully
- ✅ See their current plan in dashboard
- ✅ Access subscription/billing pages
- ✅ Upgrade plans via Razorpay
- ✅ See updated features immediately

### 🔍 **Monitoring**

Add this to monitor subscription updates:
```javascript
// In SubscriptionContext
console.log('Subscription updated:', subscription);
console.log('User can create board:', canCreateBoard());
console.log('Plan limits:', getPlanLimits());
```

This will help identify exactly where the upgrade process is failing for specific users.
