# 💳 Razorpay Test Payment Guide

## 🔧 Setup Instructions

### 1. Environment Variables
Create a `.env.local` file in your project root with:

```env
# Firebase Configuration (your actual Firebase config)
VITE_FIREBASE_API_KEY=your_firebase_api_key
VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id
VITE_FIREBASE_MEASUREMENT_ID=your_measurement_id

# Razorpay Test Configuration
VITE_RAZORPAY_KEY_ID=rzp_test_FeHhuaFiZ4jeKP
VITE_RAZORPAY_KEY_SECRET=dypGYyO7YTyysQ9IjQnmCf4c
```

### 2. Restart Development Server
After adding the `.env.local` file, restart your development server:
```bash
npm run dev
```

## 💳 Test Payment Methods

### 🎯 **IMPORTANT: These are TEST credentials - NO REAL MONEY will be charged!**

### Credit/Debit Cards (Test Mode)

#### ✅ **Successful Payments:**
```
Card Number: ************** 1111
Expiry: Any future date (e.g., 12/25)
CVV: Any 3 digits (e.g., 123)
Name: Any name
```

```
Card Number: 5555 5555 5555 4444
Expiry: Any future date (e.g., 12/25)
CVV: Any 3 digits (e.g., 123)
Name: Any name
```

#### ❌ **Failed Payments (to test error handling):**
```
Card Number: 4000 0000 0000 0002
Expiry: Any future date
CVV: Any 3 digits
Name: Any name
```

### 📱 **UPI Test IDs:**
```
UPI ID: success@razorpay
UPI ID: failure@razorpay
```

### 🏦 **Net Banking:**
- Select any test bank
- Use credentials: `razorpay` / `razorpay`

### 💰 **Wallet Testing:**
- Select any wallet option
- Use test credentials provided in the wallet interface

## 🧪 Testing Scenarios

### 1. **Pro Plan Subscription (₹299)**
1. Go to landing page → Register → Dashboard
2. Try creating 4th board → Upgrade modal appears
3. Click "Upgrade to Pro" → Payment page
4. Use test card: `************** 1111`
5. Complete payment → Should redirect to dashboard with Pro features

### 2. **Team Plan Subscription (₹599)**
1. From dashboard → Click "Upgrade" in header
2. Select Team plan → Payment page
3. Use test card: `5555 5555 5555 4444`
4. Complete payment → Should get Team features

### 3. **Failed Payment Testing**
1. Try to subscribe to any plan
2. Use failed card: `4000 0000 0000 0002`
3. Payment should fail → Error message shown
4. User remains on current plan

### 4. **Subscription Cancellation**
1. Subscribe to Pro/Team plan
2. Go to Billing page → Click "Cancel Subscription"
3. Confirm cancellation → Should revert to Free plan
4. Verify: Board limit back to 3, premium features locked

## 🔍 What to Verify

### ✅ **Successful Payment Flow:**
1. **Payment Modal Opens** - Razorpay checkout appears
2. **Test Card Accepted** - Payment processes successfully
3. **Subscription Updated** - User gets Pro/Team features immediately
4. **Dashboard Updates** - Plan badge changes, limits removed
5. **Premium Features Unlocked** - Templates, analytics, etc. available

### ✅ **Failed Payment Flow:**
1. **Error Handling** - Clear error message shown
2. **No Plan Change** - User remains on current plan
3. **Retry Option** - User can try payment again

### ✅ **Cancellation Flow:**
1. **Confirmation Modal** - Clear explanation of what happens
2. **Revert to Free** - Plan changes to Free immediately
3. **Feature Restrictions** - Premium features locked again
4. **Board Limits** - 3-board limit enforced again

## 🚨 Important Notes

### 🔒 **Security:**
- These are TEST keys only - never use in production
- No real money is processed in test mode
- All transactions are simulated

### 🎯 **Test Mode Indicators:**
- Razorpay checkout will show "Test Mode" 
- Payment confirmations will mention test environment
- No actual bank/card charges occur

### 📊 **Monitoring:**
- Check Razorpay dashboard for test transactions
- Monitor browser console for any errors
- Verify Firebase subscription data updates

## 🐛 Troubleshooting

### **Payment Modal Not Opening:**
- Check browser console for errors
- Verify `.env.local` file exists and has correct keys
- Ensure development server was restarted after adding env file

### **Payment Succeeds but Features Don't Unlock:**
- Check browser console for Firebase errors
- Verify subscription data in Firebase console
- Check if `upgradePlan` function is being called

### **Subscription Cancellation Not Working:**
- Check Firebase permissions
- Verify `cancelSubscription` function in console
- Check if subscription document is being updated

## 🎉 Success Indicators

When everything works correctly, you should see:

1. **Smooth Payment Flow** - No errors, quick processing
2. **Immediate Feature Access** - Premium features unlock instantly
3. **Proper Plan Display** - Correct plan badge in header
4. **Usage Limit Changes** - Board limits updated correctly
5. **Analytics Access** - Premium analytics features available
6. **Template Access** - Premium templates unlocked

## 📞 Support

If you encounter issues:
1. Check browser console for errors
2. Verify all environment variables are set
3. Ensure Firebase rules allow subscription updates
4. Test with different browsers/devices

**Remember: This is all in TEST MODE - no real payments are processed!** 🎯
